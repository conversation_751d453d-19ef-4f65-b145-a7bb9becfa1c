{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning"}, "Database": {"Enabled": true, "LogLevel": "Information", "Source": "DISAdmin"}}, "FileLogging": {"Enabled": true, "LogDirectory": "Logs", "LogLevel": "Warning", "RetainedFileCountLimit": 30, "FileSizeLimitBytes": 10485760, "RollingInterval": "Day", "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}, "DISApiSettings": {"BaseUrl": "https://localhost:40443", "HealthEndpoint": "/api/health", "TimeoutSeconds": 10, "IgnoreCertificateErrors": true}}