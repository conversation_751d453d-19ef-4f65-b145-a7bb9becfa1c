using DISAdmin.Core.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace DISAdmin.Core.Services;

/// <summary>
/// Služba pro kontrolu stavu DISApi
/// </summary>
public interface IDISApiHealthService
{
    /// <summary>
    /// Ko<PERSON><PERSON><PERSON><PERSON>, zda je DISApi dostupná
    /// </summary>
    /// <returns>True pokud je DISApi dostupná, jinak false</returns>
    Task<bool> IsApiRunningAsync();

    /// <summary>
    /// Získá detailní informace o stavu DISApi
    /// </summary>
    /// <returns>Informace o stavu API</returns>
    Task<DISApiHealthInfo> GetHealthInfoAsync();
}

/// <summary>
/// Informace o stavu DISApi
/// </summary>
public class DISApiHealthInfo
{
    public bool IsRunning { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public int? StatusCode { get; set; }
    public string? StatusDescription { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Implementace služby pro kontrolu stavu DISApi
/// </summary>
public class DISApiHealthService : IDISApiHealthService
{
    private readonly DISApiSettings _settings;
    private readonly ILogger<DISApiHealthService> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public DISApiHealthService(
        IOptions<DISApiSettings> settings,
        ILogger<DISApiHealthService> logger,
        IHttpClientFactory httpClientFactory)
    {
        _settings = settings.Value;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<bool> IsApiRunningAsync()
    {
        var healthInfo = await GetHealthInfoAsync();
        return healthInfo.IsRunning;
    }

    public async Task<DISApiHealthInfo> GetHealthInfoAsync()
    {
        var healthInfo = new DISApiHealthInfo();
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            // Vytvoření HTTP klienta s konfigurací
            var httpClient = CreateHttpClient();

            // Logování URL pro diagnostiku
            _logger.LogDebug($"Kontrola stavu DISApi na URL: {_settings.HealthCheckUrl}");

            // Provedení health check požadavku
            var response = await httpClient.GetAsync(_settings.HealthCheckUrl);

            stopwatch.Stop();
            healthInfo.ResponseTime = stopwatch.Elapsed;
            healthInfo.StatusCode = (int)response.StatusCode;
            healthInfo.StatusDescription = response.ReasonPhrase;

            // Logování výsledku
            _logger.LogDebug($"Odpověď z health endpointu: {response.StatusCode} {response.ReasonPhrase}, doba odezvy: {healthInfo.ResponseTime.TotalMilliseconds}ms");

            healthInfo.IsRunning = response.IsSuccessStatusCode;

            if (!healthInfo.IsRunning)
            {
                healthInfo.ErrorMessage = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
            }
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            healthInfo.ResponseTime = stopwatch.Elapsed;
            healthInfo.IsRunning = false;
            healthInfo.ErrorMessage = ex.Message;

            _logger.LogWarning(ex, $"Chyba při kontrole stavu DISApi na URL {_settings.HealthCheckUrl}: {ex.Message}");

            // Pokud je to HttpRequestException, logujeme více detailů
            if (ex is HttpRequestException httpEx)
            {
                _logger.LogWarning($"HTTP chyba při připojení na {_settings.HealthCheckUrl}: {httpEx.StatusCode}, {httpEx.Message}");
            }
        }

        return healthInfo;
    }

    private HttpClient CreateHttpClient()
    {
        var httpClient = _httpClientFactory.CreateClient("DISApiHealth");

        // Nastavení timeout
        httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);

        return httpClient;
    }
}
